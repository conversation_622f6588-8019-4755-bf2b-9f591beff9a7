#include "sierrachart.h"

SCDLLName("Delta Heatmap Smooth DLL")

/*==========================================================================*/
// Function declarations
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t);

/*==========================================================================*/
// Delta Heatmap Smooth Overlay - Colors bars based on smoothed delta values
SCSFExport scsf_DeltaHeatmapSmooth(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef SmoothingPeriod = sc.Input[0];
    SCInputRef GradientScale = sc.Input[1];
    SCInputRef StrongBuyColor = sc.Input[2];
    SCInputRef StrongSellColor = sc.Input[3];
    SCInputRef NeutralColor = sc.Input[4];
    
    // Subgraph references for internal calculations
    SCSubgraphRef SmoothedDelta = sc.Subgraph[0];
    SCSubgraphRef AvgAbsDelta = sc.Subgraph[1];
    
    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Delta Heatmap Smooth";
        sc.StudyDescription = "Heatmap overlay that colors candle bodies, borders and wicks based on smoothed delta values (Buy vs Sell control).";
        
        sc.GraphRegion = 0; // Main price graph
        sc.AutoLoop = 1;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        
        // Input parameters
        SmoothingPeriod.Name = "Lookback Smoothing (bars)";
        SmoothingPeriod.SetInt(20);
        SmoothingPeriod.SetIntLimits(1, 500);
        
        GradientScale.Name = "Gradient Scale (× Avg|Δ|)";
        GradientScale.SetFloat(2.0f);
        GradientScale.SetFloatLimits(0.1f, 10.0f);
        
        StrongBuyColor.Name = "Strong Buy Color";
        StrongBuyColor.SetColor(RGB(0, 179, 255)); // Blue
        
        StrongSellColor.Name = "Strong Sell Color";
        StrongSellColor.SetColor(RGB(255, 0, 213)); // Magenta
        
        NeutralColor.Name = "Neutral Color";
        NeutralColor.SetColor(RGB(128, 128, 128)); // Gray
        
        // Configure subgraphs for internal calculations
        SmoothedDelta.Name = "Smoothed Delta";
        SmoothedDelta.DrawStyle = DRAWSTYLE_IGNORE;

        AvgAbsDelta.Name = "Average Absolute Delta";
        AvgAbsDelta.DrawStyle = DRAWSTYLE_IGNORE;

        // Configure main subgraph for bar coloring overlay
        sc.Subgraph[2].Name = "Delta Heatmap";
        sc.Subgraph[2].DrawStyle = DRAWSTYLE_COLOR_BAR_CANDLE_FILL;
        sc.Subgraph[2].PrimaryColor = RGB(128, 128, 128);
        sc.Subgraph[2].DrawZeros = 1;
        sc.Subgraph[2].LineWidth = 1;
        
        return;
    }
    
    // Ensure we have volume at price data
    if (sc.VolumeAtPriceForBars == NULL)
        return;
    
    // Calculate smoothed delta using weighted moving average
    float weightedSum = 0.0f;
    float weightSum = 0.0f;
    int period = SmoothingPeriod.GetInt();
    
    for (int i = 0; i < period && sc.Index - i >= 0; i++)
    {
        int barIndex = sc.Index - i;
        
        // Calculate delta for this bar
        float barDelta = 0.0f;
        
        // Get price range for the bar
        int lowTick = sc.PriceValueToTicks(sc.Low[barIndex]);
        int highTick = sc.PriceValueToTicks(sc.High[barIndex]);
        
        // Sum bid and ask volumes across all price levels in the bar
        for (int priceTick = lowTick; priceTick <= highTick; priceTick++)
        {
            unsigned int bidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(barIndex, priceTick);
            unsigned int askVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(barIndex, priceTick);
            barDelta += (float)askVol - (float)bidVol; // Delta = Ask Volume - Bid Volume
        }
        
        // Weight: more recent bars get higher weight
        float weight = (float)(period - i);
        weightedSum += barDelta * weight;
        weightSum += weight;
    }
    
    SmoothedDelta[sc.Index] = (weightSum > 0) ? weightedSum / weightSum : 0.0f;
    
    // Calculate average absolute delta for normalization
    float absSum = 0.0f;
    int count = 0;
    
    for (int i = 0; i < period && sc.Index - i >= 0; i++)
    {
        int barIndex = sc.Index - i;
        
        float barDelta = 0.0f;
        int lowTick = sc.PriceValueToTicks(sc.Low[barIndex]);
        int highTick = sc.PriceValueToTicks(sc.High[barIndex]);
        
        for (int priceTick = lowTick; priceTick <= highTick; priceTick++)
        {
            unsigned int bidVol = sc.VolumeAtPriceForBars->GetBidVolumeAtPrice(barIndex, priceTick);
            unsigned int askVol = sc.VolumeAtPriceForBars->GetAskVolumeAtPrice(barIndex, priceTick);
            barDelta += (float)askVol - (float)bidVol;
        }
        
        absSum += fabs(barDelta);
        count++;
    }
    
    AvgAbsDelta[sc.Index] = (count > 0) ? absSum / count : 1.0f;
    
    // Map delta to color
    float smoothedDelta = SmoothedDelta[sc.Index];
    float avgAbsDelta = AvgAbsDelta[sc.Index];
    float scale = GradientScale.GetFloat();
    
    COLORREF barColor = NeutralColor.GetColor();
    
    if (avgAbsDelta > 0 && scale > 0)
    {
        float normalizedDelta = smoothedDelta / (avgAbsDelta * scale);
        
        // Clamp to [-1, 1]
        if (normalizedDelta > 1.0f) normalizedDelta = 1.0f;
        if (normalizedDelta < -1.0f) normalizedDelta = -1.0f;
        
        if (normalizedDelta >= 0)
        {
            // Interpolate between neutral and strong buy color
            barColor = LerpColor(NeutralColor.GetColor(), StrongBuyColor.GetColor(), normalizedDelta);
        }
        else
        {
            // Interpolate between neutral and strong sell color
            barColor = LerpColor(NeutralColor.GetColor(), StrongSellColor.GetColor(), -normalizedDelta);
        }
    }
    
    // Apply color to the bar
    sc.DataStartIndex = period - 1; // Don't color bars until we have enough data

    if (sc.Index >= sc.DataStartIndex)
    {
        // Apply color to the main bar coloring subgraph
        sc.Subgraph[2].DataColor[sc.Index] = barColor;
        sc.Subgraph[2][sc.Index] = normalizedDelta; // Store the normalized value for reference
    }
}

/*==========================================================================*/
// Helper function to interpolate between two colors
COLORREF LerpColor(COLORREF color1, COLORREF color2, float t)
{
    if (t < 0.0f) t = 0.0f;
    if (t > 1.0f) t = 1.0f;
    
    int r1 = GetRValue(color1);
    int g1 = GetGValue(color1);
    int b1 = GetBValue(color1);
    
    int r2 = GetRValue(color2);
    int g2 = GetGValue(color2);
    int b2 = GetBValue(color2);
    
    int r = (int)(r1 + (r2 - r1) * t);
    int g = (int)(g1 + (g2 - g1) * t);
    int b = (int)(b1 + (b2 - b1) * t);
    
    // Clamp values to valid range
    r = max(0, min(255, r));
    g = max(0, min(255, g));
    b = max(0, min(255, b));
    
    return RGB(r, g, b);
}
